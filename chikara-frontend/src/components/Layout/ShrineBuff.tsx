import { getNextMidnightDate } from "@/helpers/dateHelpers";
import { Link } from "react-router-dom";
import { CountdownTimer } from "./CountdownTimer";
import { useGetActiveShrineBuffs } from "@/features/shrine/api/useGetActiveShrineBuffs";

// Type for individual shrine buff
interface ShrineBuff {
    buffType: string;
    description: string;
    value: number;
}

export default function ShrineBuff({ isMobile = false }) {
    const { data: activeShrineBuffs, isLoading } = useGetActiveShrineBuffs();

    const midnightDate = getNextMidnightDate();

    // Don't show if loading, no data, or data is false (no active buffs)
    if (isLoading || !activeShrineBuffs || activeShrineBuffs === false) return null;

    // Don't show if activeShrineBuffs is an empty object
    if (typeof activeShrineBuffs === "object" && Object.keys(activeShrineBuffs).length === 0) return null;

    // Type assertion since we've already checked that activeShrineBuffs is a valid object
    const shrineBuffsObject = activeShrineBuffs as Record<string, ShrineBuff>;
    const activeBuffs = Object.values(shrineBuffsObject);

    if (isMobile) {
        return (
            <Link
                to="/shrine"
                data-tooltip-id="shrinebuff-tooltip"
                data-tooltip-buff1name={activeBuffs?.[0]?.description}
                data-tooltip-buff2name={activeBuffs?.[1]?.description}
                className="-mt-1 mx-auto mb-1.5 flex items-center gap-2"
            >
                <div className="flex gap-1">
                    <img
                        className="h-4 w-auto rotate-180"
                        src={`${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/pyBEK4l.png`}
                        alt=""
                    />
                    <p className="text-center text-custom-yellow text-sm">Shrine Buff</p>
                </div>

                <div className="relative mt-0.5 w-auto text-center text-green-500 text-xs">
                    <CountdownTimer showHours showHoursText targetDate={midnightDate} showSeconds={false} />
                </div>
            </Link>
        );
    }

    return (
        <>
            <Link
                to="/shrine"
                data-tooltip-id="shrinebuff-tooltip"
                data-tooltip-buff1name={activeBuffs?.[0]?.description}
                data-tooltip-buff2name={activeBuffs?.[1]?.description}
                className="relative cursor-pointer select-none rounded-xl border border-gray-800 bg-blue-950/50 px-2 py-1"
            >
                <div className="ml-5 flex w-fit gap-1">
                    <img
                        className="my-auto h-3 w-auto rotate-180"
                        src={`${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/pyBEK4l.png`}
                        alt=""
                    />
                    <p className="text-center text-custom-yellow text-xs 2xl:text-sm">Shrine Buff</p>
                </div>

                <div className="relative my-auto h-5 w-auto 2xl:h-6">
                    <img
                        className="h-full w-auto"
                        src={`${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/AdXczms.png`}
                        alt=""
                    />

                    <p className="absolute top-[0.2rem] right-[1.3rem] text-xs 2xl:top-[0.3rem] 2xl:right-8">
                        {" "}
                        <CountdownTimer showHours targetDate={midnightDate} showSeconds={false} />
                    </p>
                </div>
            </Link>
        </>
    );
}
